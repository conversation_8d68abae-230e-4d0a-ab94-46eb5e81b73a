﻿import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import '../controllers/novel_agent_controller.dart';
import '../controllers/novel_controller.dart';
import '../models/novel.dart';
import '../widgets/novel_agent_panel.dart';

class DaizongAIAssistantScreen extends StatefulWidget {
  final Novel novel;
  final int? initialChapterIndex;

  const DaizongAIAssistantScreen({
    super.key,
    required this.novel,
    this.initialChapterIndex,
  });

  @override
  State<DaizongAIAssistantScreen> createState() => _DaizongAIAssistantScreenState();
}

class _DaizongAIAssistantScreenState extends State<DaizongAIAssistantScreen> {
  final NovelAgentController _agentController = Get.put(NovelAgentController());
  final NovelController _novelController = Get.find<NovelController>();
  
  final List<TextEditingController> _chapterControllers = [];
  final List<TextEditingController> _chapterTitleControllers = [];
  final ScrollController _editorScrollController = ScrollController();

  late Novel _currentNovel;
  int _currentChapterIndex = 0;
  bool _hasChanges = false;
  double _aiPanelWidth = 400;

  // 响应式设计相关
  bool get _isMobile => !kIsWeb && (defaultTargetPlatform == TargetPlatform.android || defaultTargetPlatform == TargetPlatform.iOS);
  bool get _isTablet => MediaQuery.of(context).size.width > 600 && MediaQuery.of(context).size.width < 1024;
  bool get _isDesktop => MediaQuery.of(context).size.width >= 1024;

  // 手机端的Tab控制
  int _currentTabIndex = 0;
  final PageController _pageController = PageController();

  @override
  void initState() {
    super.initState();
    _currentNovel = widget.novel;
    _currentChapterIndex = widget.initialChapterIndex ?? 0;
    
    _initChapterControllers();
    _initAgent();
  }

  void _initChapterControllers() {
    // 清空现有控制器
    for (final controller in _chapterControllers) {
      controller.dispose();
    }
    for (final controller in _chapterTitleControllers) {
      controller.dispose();
    }

    _chapterControllers.clear();
    _chapterTitleControllers.clear();

    print('初始化控制器，章节数量: ${_currentNovel.chapters.length}');

    for (int i = 0; i < _currentNovel.chapters.length; i++) {
      final chapter = _currentNovel.chapters[i];

      // 内容控制器
      final contentController = TextEditingController(text: chapter.content);
      contentController.addListener(_onTextChanged);
      _chapterControllers.add(contentController);

      // 标题控制器
      final titleController = TextEditingController(text: chapter.title);
      titleController.addListener(_onTitleChanged);
      _chapterTitleControllers.add(titleController);

      print('初始化第${i}章控制器: ${chapter.title}');
    }

    print('控制器初始化完成 - 内容控制器: ${_chapterControllers.length}, 标题控制器: ${_chapterTitleControllers.length}');
  }

  void _initAgent() async {
    await _agentController.setCurrentNovel(_currentNovel);

    if (_currentNovel.chapters.isNotEmpty &&
        _currentChapterIndex < _currentNovel.chapters.length) {
      _agentController.currentChapter.value = _currentNovel.chapters[_currentChapterIndex];
    }

    // 监听章节内容变化
    _agentController.currentChapter.listen((chapter) {
      if (chapter != null && _currentChapterIndex < _chapterControllers.length) {
        // 更新编辑器内容，但不触发 _onTextChanged
        _chapterControllers[_currentChapterIndex].removeListener(_onTextChanged);
        _chapterControllers[_currentChapterIndex].text = chapter.content;
        _chapterControllers[_currentChapterIndex].addListener(_onTextChanged);
      }
    });
  }

  void _onTextChanged() {
    if (!_hasChanges) {
      setState(() {
        _hasChanges = true;
      });
    }
  }

  void _onTitleChanged() {
    if (!_hasChanges) {
      setState(() {
        _hasChanges = true;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(context),
      body: _isMobile ? _buildMobileLayout(context) : _buildDesktopLayout(context),
      bottomNavigationBar: _isMobile ? _buildMobileBottomNavigation() : null,
    );
  }

  /// 构建AppBar
  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return PreferredSize(
      preferredSize: Size.fromHeight(_isMobile ? 56 : 48),
      child: AppBar(
        title: Text(
          _isMobile
              ? '《${_currentNovel.title}》'
              : '《${_currentNovel.title}》- 岱宗AI辅助助手',
          style: TextStyle(fontSize: _isMobile ? 18 : 16),
        ),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        elevation: 1,
        actions: [
          IconButton(
            icon: Icon(Icons.save, size: _isMobile ? 24 : 20),
            onPressed: _hasChanges ? _saveChanges : null,
            tooltip: '保存',
          ),
          if (!_isMobile && _isTablet) // 平板端显示切换按钮
            IconButton(
              icon: const Icon(Icons.swap_horiz),
              onPressed: () {
                setState(() {
                  _currentTabIndex = _currentTabIndex == 0 ? 1 : 0;
                });
              },
              tooltip: '切换视图',
            ),
        ],
      ),
    );
  }

  /// 构建手机端布局
  Widget _buildMobileLayout(BuildContext context) {
    return PageView(
      controller: _pageController,
      onPageChanged: (index) {
        setState(() {
          _currentTabIndex = index;
        });
      },
      children: [
        _buildEditor(),
        Container(
          padding: const EdgeInsets.all(8),
          child: NovelAgentPanel(
            novel: _currentNovel,
            width: MediaQuery.of(context).size.width - 16,
            isMobile: true,
          ),
        ),
      ],
    );
  }

  /// 构建桌面端布局
  Widget _buildDesktopLayout(BuildContext context) {
    if (_isTablet) {
      // 平板端使用可切换的单面板布局
      return _currentTabIndex == 0
          ? _buildEditor()
          : Container(
              padding: const EdgeInsets.all(8),
              child: NovelAgentPanel(
                novel: _currentNovel,
                width: MediaQuery.of(context).size.width - 16,
                isMobile: false,
              ),
            );
    } else {
      // 桌面端使用传统的双面板布局
      return Row(
        children: [
          // 左侧编辑器区域
          Expanded(
            child: _buildEditor(),
          ),

          // 可拖拽的分隔条
          _buildResizableHandle(context),

          // 右侧AI面板区域
          Container(
            width: _aiPanelWidth,
            child: NovelAgentPanel(
              novel: _currentNovel,
              width: _aiPanelWidth,
              isMobile: false,
            ),
          ),
        ],
      );
    }
  }

  /// 构建手机端底部导航
  Widget _buildMobileBottomNavigation() {
    return BottomNavigationBar(
      currentIndex: _currentTabIndex,
      onTap: (index) {
        setState(() {
          _currentTabIndex = index;
        });
        _pageController.animateToPage(
          index,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      },
      items: const [
        BottomNavigationBarItem(
          icon: Icon(Icons.edit),
          label: '编辑器',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.auto_fix_high),
          label: 'AI助手',
        ),
      ],
    );
  }

  /// 构建可拖拽的分隔条
  Widget _buildResizableHandle(BuildContext context) {
    return MouseRegion(
      cursor: SystemMouseCursors.resizeColumn,
      child: GestureDetector(
        onPanUpdate: (details) {
          setState(() {
            _aiPanelWidth = (_aiPanelWidth - details.delta.dx).clamp(300.0, 600.0);
          });
        },
        child: Container(
          width: 8,
          color: Colors.transparent,
          child: Center(
            child: Container(
              width: 2,
              color: Theme.of(context).dividerColor,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEditor() {
    return Column(
      children: [
        if (_currentNovel.chapters.isNotEmpty) _buildChapterSelector(),
        Expanded(
          child: Container(
            padding: const EdgeInsets.all(16),
            child: _buildEditorContent(),
          ),
        ),
      ],
    );
  }

  Widget _buildChapterSelector() {
    return Container(
      height: 36,
      padding: const EdgeInsets.symmetric(horizontal: 12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceVariant,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).dividerColor,
            width: 0.5,
          ),
        ),
      ),
      child: Row(
        children: [
          Text(
            '章节：',
            style: TextStyle(
              fontSize: 13,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(width: 6),
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: _currentNovel.chapters.asMap().entries.map((entry) {
                  final index = entry.key;
                  final chapter = entry.value;
                  final isSelected = index == _currentChapterIndex;

                  // 调试信息
                  print('章节调试: index=$index, number=${chapter.number}, title=${chapter.title}');

                  return Padding(
                    padding: const EdgeInsets.only(right: 6),
                    child: ChoiceChip(
                      label: Text(
                        '第${chapter.number}章',
                        style: const TextStyle(fontSize: 12),
                      ),
                      selected: isSelected,
                      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      visualDensity: VisualDensity.compact,
                      onSelected: (selected) {
                        if (selected) {
                          _switchToChapter(index);
                        }
                      },
                    ),
                  );
                }).toList(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEditorContent() {
    if (_currentNovel.chapters.isEmpty) {
      return const Center(
        child: Text('暂无章节内容'),
      );
    }

    if (_currentChapterIndex >= _chapterControllers.length ||
        _currentChapterIndex >= _chapterTitleControllers.length) {
      return const Center(
        child: Text('章节索引超出范围'),
      );
    }

    // 添加调试信息
    print('构建编辑器内容 - 当前章节索引: $_currentChapterIndex');
    print('章节数量: ${_currentNovel.chapters.length}');
    print('内容控制器数量: ${_chapterControllers.length}');
    print('标题控制器数量: ${_chapterTitleControllers.length}');

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 可编辑的章节标题
        Container(
          height: 40, // 收窄高度
          child: Row(
            children: [
              Text(
                '第${_currentNovel.chapters[_currentChapterIndex].number}章：',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey,
                ),
              ),
              Expanded(
                child: _currentChapterIndex < _chapterTitleControllers.length
                    ? TextField(
                        controller: _chapterTitleControllers[_currentChapterIndex],
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                        decoration: const InputDecoration(
                          border: InputBorder.none,
                          hintText: '章节标题',
                          contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                          isDense: true,
                        ),
                        maxLines: 1,
                      )
                    : Text(
                        _currentNovel.chapters[_currentChapterIndex].title,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 8), // 减少间距
        Expanded(
          child: TextField(
            controller: _chapterControllers[_currentChapterIndex],
            maxLines: null,
            expands: true,
            textAlignVertical: TextAlignVertical.top,
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              hintText: '在此编辑章节内容...',
            ),
            style: const TextStyle(
              fontSize: 16,
              height: 1.6,
            ),
          ),
        ),
      ],
    );
  }

  void _switchToChapter(int index) {
    if (index >= 0 && index < _currentNovel.chapters.length) {
      setState(() {
        _currentChapterIndex = index;
      });

      // 更新 Agent 控制器的当前章节
      _agentController.currentChapter.value = _currentNovel.chapters[index];

      // 同步编辑器内容和标题
      if (index < _chapterControllers.length && index < _currentNovel.chapters.length) {
        _chapterControllers[index].removeListener(_onTextChanged);
        _chapterControllers[index].text = _currentNovel.chapters[index].content;
        _chapterControllers[index].addListener(_onTextChanged);
      }

      if (index < _chapterTitleControllers.length && index < _currentNovel.chapters.length) {
        _chapterTitleControllers[index].removeListener(_onTitleChanged);
        _chapterTitleControllers[index].text = _currentNovel.chapters[index].title;
        _chapterTitleControllers[index].addListener(_onTitleChanged);
      }
    }
  }

  void _saveChanges() async {
    try {
      // 保存章节内容和标题
      for (int i = 0; i < _chapterControllers.length; i++) {
        if (i < _currentNovel.chapters.length) {
          // 更新内容
          _currentNovel.chapters[i].content = _chapterControllers[i].text;

          // 更新标题（使用copyWith创建新实例）
          if (i < _chapterTitleControllers.length) {
            final newTitle = _chapterTitleControllers[i].text;
            if (newTitle != _currentNovel.chapters[i].title) {
              _currentNovel.chapters[i] = _currentNovel.chapters[i].copyWith(
                title: newTitle,
                content: _chapterControllers[i].text,
              );
            }
          }
        }
      }
      
      await _novelController.updateNovel(_currentNovel);
      
      setState(() {
        _hasChanges = false;
      });
      
      Get.snackbar(
        '保存成功',
        '章节内容已保存',
        snackPosition: SnackPosition.BOTTOM,
      );
      
    } catch (e) {
      Get.snackbar(
        '保存失败',
        '保存章节内容时出错: ',
        backgroundColor: Colors.red.withOpacity(0.1),
        colorText: Colors.red[700],
      );
    }
  }

  @override
  void dispose() {
    for (final controller in _chapterControllers) {
      controller.dispose();
    }
    for (final controller in _chapterTitleControllers) {
      controller.dispose();
    }
    _editorScrollController.dispose();
    super.dispose();
  }
}
